<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_453_2279" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="34" height="34">
<circle cx="17.9996" cy="18" r="16.65" fill="#B4B4B4"/>
</mask>
<g mask="url(#mask0_453_2279)">
<g filter="url(#filter0_ii_453_2279)">
<circle cx="17.9996" cy="18" r="16.65" fill="url(#paint0_radial_453_2279)"/>
</g>
<g filter="url(#filter1_f_453_2279)">
<circle cx="18" cy="17.775" r="15.75" fill="url(#paint1_radial_453_2279)"/>
</g>
<g filter="url(#filter2_f_453_2279)">
<circle cx="17.8863" cy="14.9625" r="10.4625" fill="url(#paint2_radial_453_2279)"/>
</g>
<g filter="url(#filter3_if_453_2279)">
<circle cx="17.9988" cy="17.9999" r="15.075" fill="url(#paint3_linear_453_2279)" style="mix-blend-mode:hard-light"/>
</g>
</g>
<g filter="url(#filter4_ddddii_453_2279)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.2967 22.5754C26.3983 22.3393 26.0657 22.1925 25.8995 22.3886C24.2559 24.3276 21.3325 25.6176 18.0001 25.6176C14.6683 25.6176 11.7454 24.3281 10.1017 22.3898C9.9354 22.1938 9.60286 22.3406 9.70449 22.5767C10.905 25.3656 14.1659 27.3655 18.0003 27.3655C21.8353 27.3655 25.0966 25.365 26.2967 22.5754Z" fill="url(#paint4_radial_453_2279)"/>
</g>
<g filter="url(#filter5_ddddii_453_2279)">
<ellipse cx="1.8" cy="2.1375" rx="1.8" ry="2.1375" transform="matrix(-1 0 0 1 24.5254 12.6)" fill="url(#paint5_radial_453_2279)"/>
</g>
<g filter="url(#filter6_ddddii_453_2279)">
<ellipse cx="13.2746" cy="14.7375" rx="1.8" ry="2.1375" fill="url(#paint6_radial_453_2279)"/>
</g>
<defs>
<filter id="filter0_ii_453_2279" x="1.34961" y="1.12498" width="33.3008" height="33.525" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.925"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.823529 0 0 0 0 0.403451 0 0 0 0 0.0156863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_453_2279" result="effect2_innerShadow_453_2279"/>
</filter>
<filter id="filter1_f_453_2279" x="1.575" y="1.35002" width="32.85" height="32.85" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.3375" result="effect1_foregroundBlur_453_2279"/>
</filter>
<filter id="filter2_f_453_2279" x="0.673828" y="-2.25" width="34.4258" height="34.425" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.375" result="effect1_foregroundBlur_453_2279"/>
</filter>
<filter id="filter3_if_453_2279" x="2.69883" y="2.69993" width="30.6004" height="30.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.225"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2279"/>
<feGaussianBlur stdDeviation="0.1125" result="effect2_foregroundBlur_453_2279"/>
</filter>
<filter id="filter4_ddddii_453_2279" x="9.01055" y="21.3974" width="17.9809" height="7.09312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2279" result="effect2_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2279" result="effect3_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2279" result="effect4_dropShadow_453_2279"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2279" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2279" result="effect6_innerShadow_453_2279"/>
</filter>
<filter id="filter5_ddddii_453_2279" x="20.2508" y="11.7" width="4.94961" height="6.30002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2279" result="effect2_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2279" result="effect3_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2279" result="effect4_dropShadow_453_2279"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2279" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2279" result="effect6_innerShadow_453_2279"/>
</filter>
<filter id="filter6_ddddii_453_2279" x="10.7996" y="11.7" width="4.94961" height="6.30002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2279" result="effect2_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2279" result="effect3_dropShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2279" result="effect4_dropShadow_453_2279"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2279" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2279"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2279" result="effect6_innerShadow_453_2279"/>
</filter>
<radialGradient id="paint0_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.9996 13.725) rotate(90) scale(22.1709 22.0211)">
<stop offset="0.197917" stop-color="#FFEF5B"/>
<stop offset="0.911458" stop-color="#F17B00"/>
<stop offset="0.994792" stop-color="#D54D01"/>
</radialGradient>
<radialGradient id="paint1_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18 8.71028) rotate(90) scale(25.9933 20.925)">
<stop stop-color="#FFDF1E"/>
<stop offset="0.681992" stop-color="#FFDF1E" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.8863 6.075) rotate(90) scale(19.35)">
<stop offset="0.209302" stop-color="white"/>
<stop offset="0.418605" stop-color="#FFF65B"/>
<stop offset="0.860465" stop-color="#FFDD30"/>
</radialGradient>
<linearGradient id="paint3_linear_453_2279" x1="17.9988" y1="2.92493" x2="17.9988" y2="14.9396" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFFF8"/>
<stop offset="1" stop-color="#FFCF2C" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.0006 21.4137) rotate(90) scale(8.60657 9.89483)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
<radialGradient id="paint5_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1.8 2.1375) rotate(90) scale(2.1375 1.8)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
<radialGradient id="paint6_radial_453_2279" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.2746 14.7375) rotate(90) scale(2.1375 1.8)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
</defs>
</svg>
