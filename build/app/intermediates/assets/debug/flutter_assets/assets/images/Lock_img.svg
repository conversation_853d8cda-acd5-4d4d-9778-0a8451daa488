<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1_4548" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="70" height="70">
<rect width="70" height="70" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_1_4548)">
<g filter="url(#filter0_b_1_4548)">
<mask id="path-2-outside-1_1_4548" maskUnits="userSpaceOnUse" x="12.1676" y="-7.42474" width="48.5891" height="40.184" fill="black">
<rect fill="white" x="12.1676" y="-7.42474" width="48.5891" height="40.184"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.6332 18.4454L21.3405 16.8959C24.4656 10.0503 31.8758 6.72616 37.8906 9.47188C43.9053 12.2176 46.2482 19.994 43.1232 26.8396L42.4158 28.3891"/>
</mask>
<path d="M28.4657 22.0209L29.173 20.4714L13.5081 13.3204L12.8007 14.8698L28.4657 22.0209ZM29.173 20.4714C29.8906 18.8996 31.0205 17.9074 32.0559 17.4429C33.071 16.9876 33.8254 17.0808 34.315 17.3044L41.4661 1.6394C30.2041 -3.5017 18.1591 3.13194 13.5081 13.3204L29.173 20.4714ZM34.315 17.3044C34.8047 17.5279 35.3693 18.0367 35.6903 19.1021C36.0176 20.1886 36.0082 21.6923 35.2907 23.2641L50.9556 30.4152C55.6066 20.2268 52.7281 6.7805 41.4661 1.6394L34.315 17.3044ZM35.2907 23.2641L34.5833 24.8136L50.2483 31.9647L50.9556 30.4152L35.2907 23.2641Z" fill="url(#paint0_linear_1_4548)" mask="url(#path-2-outside-1_1_4548)"/>
</g>
<g filter="url(#filter1_b_1_4548)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.1649 64.2881H51.9892C55.9478 64.2881 59.1541 61.0818 59.1541 57.1232V28.4637C59.1541 24.5051 55.9478 21.2988 51.9892 21.2988H16.1649C12.2063 21.2988 9 24.5051 9 28.4637V57.1232C9 61.0818 12.2063 64.2881 16.1649 64.2881ZM34.0761 39.5019C33.9881 39.5019 33.9625 39.4233 34.0505 39.42C34.0676 39.4194 34.0847 39.4194 34.1018 39.42C34.19 39.4233 34.1644 39.5019 34.0761 39.5019ZM30.7623 36.7923C32.5926 34.962 35.5602 34.962 37.3905 36.7923C39.2208 38.6226 39.2208 41.5902 37.3905 43.4205C36.4799 44.3311 35.5761 45.4152 35.5761 46.703V49.958C35.5761 50.7865 34.9046 51.458 34.0761 51.458C33.2477 51.458 32.5761 50.7865 32.5761 49.958V46.7023C32.5761 45.4148 31.6727 44.3309 30.7623 43.4205C28.932 41.5902 28.932 38.6226 30.7623 36.7923Z" fill="url(#paint1_linear_1_4548)"/>
</g>
<g filter="url(#filter2_b_1_4548)">
<path d="M50.788 68.4555C43.3481 68.4555 37.3147 62.4221 37.3147 54.9821C37.3147 47.5421 43.3481 41.5088 50.788 41.5088C58.2307 41.5088 64.2614 47.5421 64.2614 54.9821C64.2614 62.4221 58.2307 68.4555 50.788 68.4555Z" fill="#96C0FF"/>
<path d="M50.788 68.1495C43.517 68.1495 37.6206 62.2531 37.6206 54.9821C37.6206 47.7111 43.517 41.8147 50.788 41.8147C58.0617 41.8147 63.9554 47.7111 63.9554 54.9821C63.9554 62.2532 58.0617 68.1495 50.788 68.1495Z" stroke="url(#paint2_linear_1_4548)" stroke-width="0.611895"/>
</g>
<g filter="url(#filter3_bdii_1_4548)">
<path d="M55.5437 52.604L50.094 58.1518L46.8257 54.8227" stroke="url(#paint3_linear_1_4548)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_b_1_4548" x="-0.504489" y="-13.4464" width="66.8955" height="58.7165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.65264"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_4548"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_4548" result="shape"/>
</filter>
<filter id="filter1_b_1_4548" x="-4.30527" y="7.99356" width="76.7646" height="69.5998" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.65264"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_4548"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_4548" result="shape"/>
</filter>
<filter id="filter2_b_1_4548" x="24.0094" y="28.2035" width="53.5573" height="53.5573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.65264"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_4548"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_4548" result="shape"/>
</filter>
<filter id="filter3_bdii_1_4548" x="31.3257" y="37.104" width="39.718" height="36.5479" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_4548"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.184314 0 0 0 0 0.721569 0 0 0 0 0.207843 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_1_4548" result="effect2_dropShadow_1_4548"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_4548" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1_4548"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1_4548" result="effect4_innerShadow_1_4548"/>
</filter>
<linearGradient id="paint0_linear_1_4548" x1="42.3336" y1="-1.27784" x2="32.6716" y2="22.1424" gradientUnits="userSpaceOnUse">
<stop stop-color="#31A0FE"/>
<stop offset="0.996024" stop-color="#96C0FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_4548" x1="11.2222" y1="27.1669" x2="66.123" y2="52.7221" gradientUnits="userSpaceOnUse">
<stop stop-color="#96C0FF"/>
<stop offset="1" stop-color="#078CFE"/>
</linearGradient>
<linearGradient id="paint2_linear_1_4548" x1="51.9108" y1="37.4668" x2="51.35" y2="63.0537" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.996024" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1_4548" x1="52.4107" y1="50.8703" x2="50.8182" y2="65.3165" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.88"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
