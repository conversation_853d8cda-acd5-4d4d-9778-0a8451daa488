<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_453_2278" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="34" height="34">
<circle cx="17.9996" cy="18" r="16.65" fill="#B4B4B4"/>
</mask>
<g mask="url(#mask0_453_2278)">
<g filter="url(#filter0_ii_453_2278)">
<circle cx="17.9996" cy="18" r="16.65" fill="url(#paint0_radial_453_2278)"/>
</g>
<g filter="url(#filter1_f_453_2278)">
<circle cx="18" cy="17.775" r="15.75" fill="url(#paint1_radial_453_2278)"/>
</g>
<g filter="url(#filter2_f_453_2278)">
<circle cx="17.8863" cy="14.9625" r="10.4625" fill="url(#paint2_radial_453_2278)"/>
</g>
<g filter="url(#filter3_if_453_2278)">
<circle cx="17.9988" cy="17.9999" r="15.075" fill="url(#paint3_linear_453_2278)" style="mix-blend-mode:hard-light"/>
</g>
</g>
<g filter="url(#filter4_ddddii_453_2278)">
<ellipse cx="1.8" cy="2.1375" rx="1.8" ry="2.1375" transform="matrix(-1 0 0 1 24.5254 12.6)" fill="url(#paint4_radial_453_2278)"/>
</g>
<g filter="url(#filter5_ddddii_453_2278)">
<ellipse cx="13.2746" cy="14.7375" rx="1.8" ry="2.1375" fill="url(#paint5_radial_453_2278)"/>
</g>
<path d="M24.8943 25.809H11.3216C10.9087 25.809 10.5664 25.4667 10.5664 25.0539C10.5664 24.641 10.9087 24.2987 11.3216 24.2987H24.8943C25.3072 24.2987 25.6495 24.641 25.6495 25.0539C25.6495 25.4667 25.3072 25.809 24.8943 25.809Z" fill="url(#paint6_radial_453_2278)"/>
<path d="M11.2511 24.7015H24.9749C25.2669 24.7015 25.5287 24.8726 25.6495 25.1143C25.6495 25.0941 25.6495 25.074 25.6495 25.0539C25.6495 24.641 25.3072 24.2987 24.8943 24.2987H11.3216C10.9087 24.2987 10.5664 24.641 10.5664 25.0539C10.5664 25.074 10.5664 25.0941 10.5664 25.1143C10.6973 24.8626 10.949 24.7015 11.2511 24.7015Z" fill="url(#paint7_linear_453_2278)"/>
<defs>
<filter id="filter0_ii_453_2278" x="1.34961" y="1.12498" width="33.3008" height="33.525" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.925"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.823529 0 0 0 0 0.403451 0 0 0 0 0.0156863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_453_2278" result="effect2_innerShadow_453_2278"/>
</filter>
<filter id="filter1_f_453_2278" x="1.575" y="1.35002" width="32.85" height="32.85" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.3375" result="effect1_foregroundBlur_453_2278"/>
</filter>
<filter id="filter2_f_453_2278" x="0.673828" y="-2.25" width="34.4258" height="34.425" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.375" result="effect1_foregroundBlur_453_2278"/>
</filter>
<filter id="filter3_if_453_2278" x="2.69883" y="2.69993" width="30.6004" height="30.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.225"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2278"/>
<feGaussianBlur stdDeviation="0.1125" result="effect2_foregroundBlur_453_2278"/>
</filter>
<filter id="filter4_ddddii_453_2278" x="20.2508" y="11.7" width="4.94961" height="6.30002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2278" result="effect2_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2278" result="effect3_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2278" result="effect4_dropShadow_453_2278"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2278" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2278" result="effect6_innerShadow_453_2278"/>
</filter>
<filter id="filter5_ddddii_453_2278" x="10.7996" y="11.7" width="4.94961" height="6.30002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2278" result="effect2_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2278" result="effect3_dropShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2278" result="effect4_dropShadow_453_2278"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2278" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2278"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2278" result="effect6_innerShadow_453_2278"/>
</filter>
<radialGradient id="paint0_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.9996 13.725) rotate(90) scale(22.1709 22.0211)">
<stop offset="0.197917" stop-color="#FFEF5B"/>
<stop offset="0.911458" stop-color="#F17B00"/>
<stop offset="0.994792" stop-color="#D54D01"/>
</radialGradient>
<radialGradient id="paint1_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18 8.71028) rotate(90) scale(25.9933 20.925)">
<stop stop-color="#FFDF1E"/>
<stop offset="0.681992" stop-color="#FFDF1E" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.8863 6.075) rotate(90) scale(19.35)">
<stop offset="0.209302" stop-color="white"/>
<stop offset="0.418605" stop-color="#FFF65B"/>
<stop offset="0.860465" stop-color="#FFDD30"/>
</radialGradient>
<linearGradient id="paint3_linear_453_2278" x1="17.9988" y1="2.92493" x2="17.9988" y2="14.9396" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFFF8"/>
<stop offset="1" stop-color="#FFCF2C" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1.8 2.1375) rotate(90) scale(2.1375 1.8)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
<radialGradient id="paint5_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.2746 14.7375) rotate(90) scale(2.1375 1.8)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
<radialGradient id="paint6_radial_453_2278" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.1079 25.0497) scale(5.35936 0.96415)">
<stop offset="0.00132565" stop-color="#A06106"/>
<stop offset="1" stop-color="#643800"/>
</radialGradient>
<linearGradient id="paint7_linear_453_2278" x1="10.5664" y1="24.7009" x2="25.6495" y2="24.7009" gradientUnits="userSpaceOnUse">
<stop offset="0.00132565" stop-color="#3C2200"/>
<stop offset="1" stop-color="#512D00"/>
</linearGradient>
</defs>
</svg>
