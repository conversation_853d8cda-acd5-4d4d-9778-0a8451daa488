<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_453_2280" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="34" height="34">
<circle cx="17.9996" cy="18" r="16.65" fill="#B4B4B4"/>
</mask>
<g mask="url(#mask0_453_2280)">
<g filter="url(#filter0_ii_453_2280)">
<circle cx="17.9996" cy="18" r="16.65" fill="url(#paint0_radial_453_2280)"/>
</g>
<g filter="url(#filter1_f_453_2280)">
<circle cx="18" cy="17.775" r="15.75" fill="url(#paint1_radial_453_2280)"/>
</g>
<g filter="url(#filter2_f_453_2280)">
<circle cx="17.8863" cy="14.9625" r="10.4625" fill="url(#paint2_radial_453_2280)"/>
</g>
<g filter="url(#filter3_if_453_2280)">
<circle cx="17.9988" cy="17.9999" r="15.075" fill="url(#paint3_linear_453_2280)" style="mix-blend-mode:hard-light"/>
</g>
</g>
<g filter="url(#filter4_ddddii_453_2280)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.3567 25.5123C26.4048 25.3346 26.2214 25.1908 26.0525 25.2643C23.9549 26.1768 21.1199 26.775 17.9996 26.775C14.8794 26.775 12.0444 26.1768 9.94673 25.2643C9.77786 25.1908 9.59445 25.3346 9.64257 25.5123C10.4747 28.5859 13.9002 31.275 17.9996 31.275C22.099 31.275 25.5246 28.5859 26.3567 25.5123Z" fill="url(#paint4_radial_453_2280)"/>
</g>
<g filter="url(#filter5_ii_453_2280)">
<path d="M23.721 10.7451L23.6514 10.7185C22.1903 10.1601 20.5532 10.8919 19.9948 12.353L19.9946 12.3534C18.6776 15.7994 22.2629 22.0763 22.2629 22.0763C22.2629 22.0763 29.1206 19.7905 30.4376 16.3446L30.4378 16.3441C30.9962 14.883 30.2644 13.2458 28.8033 12.6874L28.7337 12.6608C27.5696 12.2159 26.2938 12.59 25.5408 13.4993C25.5862 12.3195 24.8851 11.19 23.721 10.7451Z" fill="url(#paint5_linear_453_2280)"/>
</g>
<mask id="mask1_453_2280" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="20" y="11" width="11" height="11">
<path d="M23.1624 21.0352C22.8268 21.1584 22.4531 21.0155 22.2852 20.7C21.8458 19.8742 21.4549 19.0207 21.1173 18.1496C20.7627 17.2346 20.4483 16.2292 20.2962 15.2629C20.1437 14.2936 20.1579 13.3848 20.4389 12.6495L20.4391 12.649C20.9031 11.4349 22.302 10.8414 23.5636 11.3236L23.6298 11.3489C24.6346 11.7329 25.1965 12.8229 25.1703 13.808L25.178 14.4459L25.6092 13.9757C26.2467 13.2242 27.3923 12.7869 28.3971 13.1708L28.4633 13.1962C29.7249 13.6783 30.3714 15.0535 29.9074 16.2676L29.9072 16.2681C29.6262 17.0034 29.0307 17.6901 28.2705 18.3105C27.5128 18.9291 26.608 19.4685 25.7335 19.9138C24.901 20.3377 24.0405 20.7129 23.1624 21.0352Z" fill="url(#paint6_linear_453_2280)"/>
</mask>
<g mask="url(#mask1_453_2280)">
<path d="M23.1624 21.0352C22.8268 21.1584 22.4531 21.0155 22.2852 20.7C21.8458 19.8742 21.4549 19.0207 21.1173 18.1496C20.7627 17.2346 20.4483 16.2292 20.2962 15.2629C20.1437 14.2936 20.1579 13.3848 20.4389 12.6495L20.4391 12.649C20.9031 11.4349 22.302 10.8414 23.5636 11.3236L23.6298 11.3489C24.6346 11.7329 25.2497 12.6836 25.2235 13.6687L25.2312 14.3067L25.6625 13.8365C26.2999 13.085 27.3923 12.7869 28.3971 13.1708L28.4633 13.1962C29.7249 13.6783 30.3714 15.0535 29.9074 16.2676L29.9072 16.2681C29.6262 17.0034 29.0307 17.6901 28.2705 18.3105C27.5128 18.9291 26.608 19.4685 25.7335 19.9138C24.901 20.3377 24.0405 20.7129 23.1624 21.0352Z" fill="url(#paint7_linear_453_2280)"/>
<g filter="url(#filter6_f_453_2280)">
<ellipse cx="24.0575" cy="17.3769" rx="6.40969" ry="4.13649" transform="rotate(20.9159 24.0575 17.3769)" fill="#FF3E30"/>
</g>
<g filter="url(#filter7_f_453_2280)">
<path d="M19.6289 14.767C19.7429 16.0884 20.0513 17.4519 20.4153 18.6878C20.7676 19.8842 21.1882 21.0515 21.6718 22.1753C21.8321 22.5478 22.2662 22.7138 22.6341 22.5431C23.744 22.0282 24.8359 21.439 25.8963 20.7825C26.9918 20.1044 28.131 19.2941 29.0972 18.3856L24.6193 19.7336C23.6455 20.0268 22.5964 19.6259 22.0663 18.7579L19.6289 14.767Z" fill="#BF0E07"/>
</g>
</g>
<g filter="url(#filter8_ii_453_2280)">
<path d="M7.20452 12.7067L7.13517 12.734C5.67981 13.3072 4.96467 14.9517 5.53787 16.407L5.53806 16.4075C6.88993 19.8399 13.7705 22.0561 13.7705 22.0561C13.7705 22.0561 17.2919 15.7431 15.94 12.3107L15.9399 12.3102C15.3667 10.8548 13.7222 10.1397 12.2668 10.7129L12.1975 10.7402C11.038 11.1969 10.3483 12.3335 10.4057 13.5127C9.64352 12.6111 8.364 12.25 7.20452 12.7067Z" fill="url(#paint8_linear_453_2280)"/>
</g>
<mask id="mask2_453_2280" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="11" width="11" height="11">
<path d="M13.7344 20.6801C13.5697 20.9973 13.1974 21.144 12.8606 21.0242C11.9792 20.7109 11.115 20.3444 10.2782 19.929C9.39927 19.4926 8.48905 18.9623 7.72507 18.3515C6.95869 17.7388 6.35618 17.0582 6.06772 16.3258L6.06755 16.3253C5.59122 15.1159 6.22378 13.7343 7.48042 13.2393L7.54637 13.2134C8.54718 12.8192 9.69723 13.2449 10.3422 13.9899L10.7782 14.4557L10.7795 13.8177C10.7433 12.833 11.2941 11.7373 12.2949 11.3432L12.3609 11.3172C13.6175 10.8222 15.0224 11.4015 15.4987 12.6109L15.4989 12.6113C15.7873 13.3437 15.8107 14.2524 15.668 15.2232C15.5258 16.1909 15.2216 17.1994 14.8763 18.118C14.5475 18.9925 14.1654 19.8499 13.7344 20.6801Z" fill="url(#paint9_linear_453_2280)"/>
</mask>
<g mask="url(#mask2_453_2280)">
<path d="M13.7344 20.6801C13.5697 20.9973 13.1974 21.144 12.8606 21.0242C11.9792 20.7109 11.115 20.3444 10.2782 19.929C9.39927 19.4926 8.48905 18.9623 7.72507 18.3515C6.95869 17.7388 6.35618 17.0582 6.06772 16.3258L6.06755 16.3253C5.59122 15.1159 6.22378 13.7343 7.48042 13.2393L7.54637 13.2134C8.54718 12.8192 9.64261 13.1062 10.2876 13.8512L10.7236 14.317L10.7249 13.679C10.6886 12.6943 11.2941 11.7373 12.2949 11.3432L12.3609 11.3172C13.6175 10.8222 15.0224 11.4015 15.4987 12.6109L15.4989 12.6113C15.7873 13.3437 15.8107 14.2524 15.668 15.2232C15.5258 16.1909 15.2216 17.1994 14.8763 18.118C14.5475 18.9925 14.1654 19.8499 13.7344 20.6801Z" fill="url(#paint10_linear_453_2280)"/>
<g filter="url(#filter9_f_453_2280)">
<ellipse cx="11.9289" cy="17.3752" rx="6.40969" ry="4.13649" transform="rotate(-21.4971 11.9289 17.3752)" fill="#FF3E30"/>
</g>
<g filter="url(#filter10_f_453_2280)">
<path d="M6.89844 18.4351C7.87386 19.3337 9.0212 20.1324 10.1235 20.7994C11.1906 21.4451 12.2884 22.0232 13.4034 22.5268C13.773 22.6937 14.2055 22.5234 14.362 22.1493C14.8341 21.0206 15.2428 19.8491 15.583 18.6492C15.9344 17.4096 16.229 16.0431 16.3296 14.7206L13.9328 18.7361C13.4115 19.6094 12.3666 20.0209 11.3898 19.7376L6.89844 18.4351Z" fill="#BF0E07"/>
</g>
</g>
<defs>
<filter id="filter0_ii_453_2280" x="1.34961" y="1.12498" width="33.3008" height="33.525" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.925"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.823529 0 0 0 0 0.403451 0 0 0 0 0.0156863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_453_2280" result="effect2_innerShadow_453_2280"/>
</filter>
<filter id="filter1_f_453_2280" x="1.575" y="1.35002" width="32.85" height="32.85" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.3375" result="effect1_foregroundBlur_453_2280"/>
</filter>
<filter id="filter2_f_453_2280" x="0.673828" y="-2.25" width="34.4258" height="34.425" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.375" result="effect1_foregroundBlur_453_2280"/>
</filter>
<filter id="filter3_if_453_2280" x="2.69883" y="2.69993" width="30.6004" height="30.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.225"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2280"/>
<feGaussianBlur stdDeviation="0.1125" result="effect2_foregroundBlur_453_2280"/>
</filter>
<filter id="filter4_ddddii_453_2280" x="8.95977" y="24.3446" width="18.0805" height="8.0554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.905882 0 0 0 0 0.215686 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 0.298039 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_453_2280" result="effect2_dropShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.45"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.462918 0 0 0 0 0.109375 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect2_dropShadow_453_2280" result="effect3_dropShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.225"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 0 0.408333 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect3_dropShadow_453_2280" result="effect4_dropShadow_453_2280"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_453_2280" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.225"/>
<feGaussianBlur stdDeviation="0.3375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="overlay" in2="shape" result="effect5_innerShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.45"/>
<feGaussianBlur stdDeviation="0.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="overlay" in2="effect5_innerShadow_453_2280" result="effect6_innerShadow_453_2280"/>
</filter>
<filter id="filter5_ii_453_2280" x="19.7031" y="9.78574" width="10.9219" height="12.5887" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.745389"/>
<feGaussianBlur stdDeviation="0.745389"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.298156"/>
<feGaussianBlur stdDeviation="0.149078"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_453_2280" result="effect2_innerShadow_453_2280"/>
</filter>
<filter id="filter6_f_453_2280" x="16.0997" y="11.0966" width="15.9158" height="12.5607" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.894467" result="effect1_foregroundBlur_453_2280"/>
</filter>
<filter id="filter7_f_453_2280" x="18.809" y="13.947" width="11.1086" height="9.48324" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.409964" result="effect1_foregroundBlur_453_2280"/>
</filter>
<filter id="filter8_ii_453_2280" x="5.33984" y="9.76975" width="10.9102" height="12.5844" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.745389"/>
<feGaussianBlur stdDeviation="0.745389"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_453_2280"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.298156"/>
<feGaussianBlur stdDeviation="0.149078"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_453_2280" result="effect2_innerShadow_453_2280"/>
</filter>
<filter id="filter9_f_453_2280" x="3.9845" y="11.0768" width="15.8884" height="12.5969" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.894467" result="effect1_foregroundBlur_453_2280"/>
</filter>
<filter id="filter10_f_453_2280" x="6.07851" y="13.9007" width="11.0715" height="9.51022" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.409964" result="effect1_foregroundBlur_453_2280"/>
</filter>
<radialGradient id="paint0_radial_453_2280" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.9996 13.725) rotate(90) scale(22.1709 22.0211)">
<stop offset="0.197917" stop-color="#FFEF5B"/>
<stop offset="0.911458" stop-color="#F17B00"/>
<stop offset="0.994792" stop-color="#D54D01"/>
</radialGradient>
<radialGradient id="paint1_radial_453_2280" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18 8.71028) rotate(90) scale(25.9933 20.925)">
<stop stop-color="#FFDF1E"/>
<stop offset="0.681992" stop-color="#FFDF1E" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_453_2280" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.8863 6.075) rotate(90) scale(19.35)">
<stop offset="0.209302" stop-color="white"/>
<stop offset="0.418605" stop-color="#FFF65B"/>
<stop offset="0.860465" stop-color="#FFDD30"/>
</radialGradient>
<linearGradient id="paint3_linear_453_2280" x1="17.9988" y1="2.92493" x2="17.9988" y2="14.9396" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFFF8"/>
<stop offset="1" stop-color="#FFCF2C" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_453_2280" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.9196 23.589) rotate(90) scale(6.98048 9.72299)">
<stop stop-color="#A66707"/>
<stop offset="1" stop-color="#945706"/>
</radialGradient>
<linearGradient id="paint5_linear_453_2280" x1="26.2274" y1="11.703" x2="22.2629" y2="22.0763" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF3D30"/>
<stop offset="0.479167" stop-color="#EB2B1E"/>
<stop offset="1" stop-color="#A60A03"/>
</linearGradient>
<linearGradient id="paint6_linear_453_2280" x1="26.0135" y1="12.2599" x2="22.927" y2="20.3358" gradientUnits="userSpaceOnUse">
<stop offset="0.189655" stop-color="#CC1009"/>
<stop offset="0.564655" stop-color="#CC1009" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_453_2280" x1="22.9536" y1="20.2662" x2="26.0135" y2="12.2599" gradientUnits="userSpaceOnUse">
<stop stop-color="#CC1009"/>
<stop offset="0.295652" stop-color="#ED2416"/>
<stop offset="0.786458" stop-color="#FF958D"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint8_linear_453_2280" x1="9.70099" y1="11.7234" x2="13.7705" y2="22.0561" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF3D30"/>
<stop offset="0.479167" stop-color="#EB2B1E"/>
<stop offset="1" stop-color="#A60A03"/>
</linearGradient>
<linearGradient id="paint9_linear_453_2280" x1="9.92065" y1="12.2783" x2="13.0889" y2="20.3225" gradientUnits="userSpaceOnUse">
<stop offset="0.189655" stop-color="#CC1009"/>
<stop offset="0.564655" stop-color="#CC1009" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_453_2280" x1="13.0616" y1="20.2531" x2="9.92065" y2="12.2783" gradientUnits="userSpaceOnUse">
<stop stop-color="#CC1009"/>
<stop offset="0.295652" stop-color="#ED2416"/>
<stop offset="0.786458" stop-color="#FF958D"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
