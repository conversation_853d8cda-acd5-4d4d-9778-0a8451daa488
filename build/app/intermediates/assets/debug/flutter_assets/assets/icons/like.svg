<svg width="94" height="94" viewBox="0 0 94 94" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_7084)">
<circle cx="39" cy="47" r="31" fill="url(#paint0_radial_1_7084)"/>
</g>
<path d="M48.2689 51.0944L49.0363 46.6568C49.228 45.5484 48.3757 44.5346 47.2521 44.5346H41.6165C41.058 44.5346 40.6326 44.0333 40.723 43.4815L41.4438 39.0821C41.5609 38.3674 41.5275 37.6361 41.3456 36.9351C41.195 36.3544 40.747 35.8881 40.1577 35.6988L40 35.6481C39.6439 35.5337 39.2553 35.5603 38.9195 35.7221C38.55 35.9002 38.2796 36.225 38.1794 36.6113L37.6619 38.6061C37.4973 39.2407 37.2575 39.8534 36.9483 40.4322C36.4965 41.278 35.798 41.9548 35.0719 42.5805L33.507 43.9291C33.0657 44.3093 32.834 44.8787 32.8842 45.4594L33.7676 55.676C33.8486 56.6131 34.6321 57.3325 35.5717 57.3325H40.6281C44.4147 57.3325 47.6464 54.6941 48.2689 51.0944Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.452 43.7332C29.8885 43.7144 30.2623 44.0427 30.2999 44.4779L31.3568 56.7001C31.4246 57.4852 30.8062 58.1619 30.0166 58.1619C29.2728 58.1619 28.6714 57.5585 28.6714 56.8161V44.5482C28.6714 44.1114 29.0155 43.752 29.452 43.7332Z" fill="white"/>
<defs>
<filter id="filter0_d_1_7084" x="0" y="0" width="94" height="94" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.313726 0 0 0 0 1 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_7084"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_7084" result="shape"/>
</filter>
<radialGradient id="paint0_radial_1_7084" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(39 45) rotate(90.3588) scale(86.8438)">
<stop stop-color="#A7B7FF"/>
<stop offset="1" stop-color="#4D6EFF"/>
</radialGradient>
</defs>
</svg>
