<svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_576_9137)">
<circle cx="16" cy="16" r="12" fill="#19191A"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29 16C29 23.1797 23.1797 29 16 29C8.8203 29 3 23.1797 3 16C3 8.8203 8.8203 3 16 3C23.1797 3 29 8.8203 29 16ZM12.0605 12.0605C12.4413 11.6798 13.0586 11.6798 13.4394 12.0605L16 14.6211L18.5605 12.0606C18.9413 11.6798 19.5586 11.6798 19.9394 12.0606C20.3201 12.4413 20.3201 13.0587 19.9394 13.4394L17.3788 16L19.9393 18.5605C20.3201 18.9413 20.3201 19.5586 19.9393 19.9394C19.5586 20.3201 18.9412 20.3201 18.5605 19.9394L16 17.3788L13.4394 19.9394C13.0586 20.3202 12.4413 20.3202 12.0605 19.9394C11.6798 19.5586 11.6798 18.9413 12.0605 18.5605L14.6211 16L12.0605 13.4394C11.6798 13.0586 11.6798 12.4413 12.0605 12.0605Z" fill="white"/>
<defs>
<filter id="filter0_d_576_9137" x="1" y="3" width="30" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.27451 0 0 0 0 0.282353 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_576_9137"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_576_9137" result="shape"/>
</filter>
</defs>
</svg>
